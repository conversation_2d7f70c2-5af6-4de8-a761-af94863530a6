<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Colour Palette Generator</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>🎨 Advanced Colour Palette Generator</h1>

        <!-- Mood-Based Palettes Section -->
        <div class="section">
            <h3><i class="fas fa-heart"></i> Mood-Based Palettes</h3>
            <div class="mood-controls">
                <select id="palette-mood" class="mood-select">
                    <option value="">Select a mood or theme...</option>
                    <option value="calm">🧘 Calm & Peaceful</option>
                    <option value="energetic">⚡ Energetic & Vibrant</option>
                    <option value="romantic">💕 Romantic & Soft</option>
                    <option value="nature">🌿 Nature & Earth</option>
                    <option value="business">💼 Professional & Business</option>
                    <option value="night">🌙 Night & Dark</option>
                    <option value="ocean">🌊 Ocean & Water</option>
                    <option value="sunset">🌅 Sunset & Warm</option>
                    <option value="forest">🌲 Forest & Green</option>
                    <option value="vintage">📻 Vintage & Retro</option>
                    <option value="modern">🏢 Modern & Minimal</option>
                    <option value="autumn">🍂 Autumn & Cozy</option>
                    <option value="spring">🌸 Spring & Fresh</option>
                    <option value="winter">❄️ Winter & Cool</option>
                    <option value="tropical">🏝️ Tropical & Bright</option>
                </select>
                <button id="apply-recommended" class="btn" disabled>Apply Palette</button>
                <button id="shuffle-mood" class="btn" disabled>Shuffle Variations</button>
            </div>
            <div id="recommended-preview" class="palette-container"></div>
        </div>

        <!-- Color Harmony Generator Section -->
        <div class="section">
            <h3><i class="fas fa-palette"></i> Color Harmony Generator</h3>
            <div class="harmony-controls">
                <div class="color-input-wrapper">
                    <label for="base-color">Base Color:</label>
                    <input type="color" id="base-color" class="color-input" value="#667eea">
                </div>
                <select id="harmony-type" class="mood-select">
                    <option value="complementary">Complementary</option>
                    <option value="triadic">Triadic</option>
                    <option value="analogous">Analogous</option>
                    <option value="split-complementary">Split Complementary</option>
                    <option value="tetradic">Tetradic</option>
                    <option value="monochromatic">Monochromatic</option>
                </select>
                <button id="generate-harmony" class="btn">Generate Harmony</button>
            </div>
        </div>
        <!-- Export/Import Section -->
        <div class="section">
            <h3><i class="fas fa-download"></i> Export & Import</h3>
            <div class="export-import">
                <button id="save-palette" class="btn">💾 Save Current Palette</button>
                <button id="export-palette" class="btn">📤 Export as JSON</button>
                <label for="import-file" class="file-label">
                    <i class="fas fa-upload"></i> Import Palette
                </label>
                <input type="file" id="import-file" class="file-input" accept=".json">
                <button id="clear-saved" class="btn">🗑️ Clear Saved</button>
            </div>
            <div id="saved-palettes"></div>
        </div>

        <!-- Main Palette Display -->
        <div class="section">
            <h3><i class="fas fa-swatchbook"></i> Current Palette</h3>
            <div class="palette-container" id="main-palette">
                <div class="color-box">
                    <div class="color" style="background-color: #667eea;"></div>
                    <div class="color-info">
                        <span class="hex-value">#667EEA</span>
                        <div class="action-buttons">
                            <button class="lock-btn" title="Lock color">
                                <i class="fas fa-lock-open"></i>
                            </button>
                            <button class="copy-btn" title="Copy to clipboard">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="color-box">
                    <div class="color" style="background-color: #764ba2;"></div>
                    <div class="color-info">
                        <span class="hex-value">#764BA2</span>
                        <div class="action-buttons">
                            <button class="lock-btn" title="Lock color">
                                <i class="fas fa-lock-open"></i>
                            </button>
                            <button class="copy-btn" title="Copy to clipboard">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="color-box">
                    <div class="color" style="background-color: #f093fb;"></div>
                    <div class="color-info">
                        <span class="hex-value">#F093FB</span>
                        <div class="action-buttons">
                            <button class="lock-btn" title="Lock color">
                                <i class="fas fa-lock-open"></i>
                            </button>
                            <button class="copy-btn" title="Copy to clipboard">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="color-box">
                    <div class="color" style="background-color: #f5576c;"></div>
                    <div class="color-info">
                        <span class="hex-value">#F5576C</span>
                        <div class="action-buttons">
                            <button class="lock-btn" title="Lock color">
                                <i class="fas fa-lock-open"></i>
                            </button>
                            <button class="copy-btn" title="Copy to clipboard">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="color-box">
                    <div class="color" style="background-color: #4facfe;"></div>
                    <div class="color-info">
                        <span class="hex-value">#4FACFE</span>
                        <div class="action-buttons">
                            <button class="lock-btn" title="Lock color">
                                <i class="fas fa-lock-open"></i>
                            </button>
                            <button class="copy-btn" title="Copy to clipboard">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Buttons -->
        <div class="controls-section">
            <button id="generate-random" class="btn">
                <i class="fas fa-dice"></i> Generate Random
            </button>
            <button id="generate-gradient" class="btn">
                <i class="fas fa-layer-group"></i> Generate Gradient
            </button>
            <button id="adjust-brightness" class="btn">
                <i class="fas fa-sun"></i> Adjust Brightness
            </button>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>