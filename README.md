# 🌈 NEXUS PALETTE GENERATOR

> **A futuristic, cyberpunk-themed color palette generator with advanced AI features and accessibility tools**

![Cyberpunk Theme](https://img.shields.io/badge/Theme-Cyberpunk-00ffff?style=for-the-badge)
![HTML5](https://img.shields.io/badge/HTML5-E34F26?style=for-the-badge&logo=html5&logoColor=white)
![CSS3](https://img.shields.io/badge/CSS3-1572B6?style=for-the-badge&logo=css3&logoColor=white)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=for-the-badge&logo=javascript&logoColor=black)

## ✨ Features

### 🎨 **Core Palette Generation**
- **Random Palette Generator** - Generate beautiful random color combinations
- **Color Locking System** - Lock specific colors while generating new ones
- **One-Click Copy** - Copy hex codes to clipboard with visual feedback

### 🧠 **AI-Powered Features**
- **🤖 AI Color Name Generator** - Generate creative, unique names for colors using advanced algorithms
- **🎯 Smart Color Combinations** - Intelligent color pairing suggestions

### 🌈 **Mood-Based Palettes**
Choose from **15+ mood categories**, each with **3 unique variations**:
- 🧘 **Calm & Peaceful** - Serene, relaxing color combinations
- ⚡ **Energetic & Vibrant** - Bold, dynamic color schemes
- 💕 **Romantic & Soft** - Gentle, loving color palettes
- 🌿 **Nature & Earth** - Organic, natural color combinations
- 💼 **Professional & Business** - Corporate, sophisticated palettes
- 🌙 **Night & Dark** - Mysterious, elegant dark themes
- 🌊 **Ocean & Water** - Aquatic, flowing color schemes
- 🌅 **Sunset & Warm** - Warm, golden color combinations
- 🌲 **Forest & Green** - Fresh, woodland color palettes
- 📻 **Vintage & Retro** - Classic, nostalgic color schemes
- 🏢 **Modern & Minimal** - Clean, contemporary palettes
- 🍂 **Autumn & Cozy** - Warm, seasonal color combinations
- 🌸 **Spring & Fresh** - Light, renewal-themed palettes
- ❄️ **Winter & Cool** - Cool, crisp color schemes
- 🏝️ **Tropical & Bright** - Vibrant, exotic color combinations

### 🎨 **Color Harmony Generator**
Generate palettes based on **color theory principles**:
- **Complementary** - Colors opposite on the color wheel
- **Triadic** - Three evenly spaced colors
- **Analogous** - Adjacent colors on the wheel
- **Split Complementary** - Base color + two adjacent to its complement
- **Tetradic** - Four colors forming a rectangle
- **Monochromatic** - Variations of a single hue

### ♿ **Accessibility Features**
- **🔍 WCAG Compliance Checker** - Test color combinations for accessibility standards
- **👁️ Colorblind Simulation** - Preview palettes as seen by colorblind users
  - Protanopia (Red-blind)
  - Deuteranopia (Green-blind)
  - Tritanopia (Blue-blind)
- **Contrast Ratio Analysis** - Detailed accessibility ratings (AAA, AA, A, FAIL)

### 💾 **Export & Import System**
- **Save Palettes** - Store favorite palettes locally
- **Export as JSON** - Download palette data
- **Import Palettes** - Load saved palette files
- **Palette Management** - View, load, and delete saved palettes

### 🎮 **Advanced Generation Tools**
- **Gradient Palette Generator** - Create smooth color transitions
- **Brightness Adjustment** - Modify lightness while preserving hue
- **Shuffle Variations** - Cycle through different mood palette options

## 🚀 **Unique Cyberpunk UI Design**

### 🌟 **Visual Features**
- **Animated Grid Background** - Moving cyberpunk-style grid pattern
- **Floating Particles** - Dynamic particle effects
- **Neon Glow Effects** - Authentic cyberpunk neon lighting
- **Holographic Elements** - Futuristic UI components
- **Glass Morphism** - Modern translucent design elements
- **Geometric Clip Paths** - Unique angular button and container shapes

### 🎨 **Color Scheme**
- **Neon Cyan** (#00ffff) - Primary accent color
- **Neon Pink** (#ff0080) - Secondary accent color
- **Neon Purple** (#8000ff) - Tertiary accent color
- **Neon Green** (#00ff80) - Success/positive actions
- **Neon Yellow** (#ffff00) - Warnings/locked states

### ✨ **Animations**
- **Container Glow** - Pulsing glow effects
- **Corner Pulse** - Animated corner decorations
- **Button Hover** - Smooth transition effects
- **Text Glow** - Animated text shadows
- **Particle Movement** - Floating background elements

## 🛠️ Installation & Setup

### **Quick Start**
1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/nexus-palette-generator.git
   cd nexus-palette-generator
   ```

2. **Start a local server**
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

3. **Open in browser**
   ```
   http://localhost:8000
   ```

### **No Installation Required**
Simply open `index.html` in any modern web browser!

## 📱 **Browser Compatibility**
- ✅ **Chrome** 90+
- ✅ **Firefox** 88+
- ✅ **Safari** 14+
- ✅ **Edge** 90+

## 🎯 **Usage Guide**

### **Basic Palette Generation**
1. Click **"Generate Random"** for instant color combinations
2. Use **lock buttons** to preserve specific colors
3. Click **copy buttons** to copy hex codes

### **Mood-Based Palettes**
1. Select a mood from the dropdown
2. Click **"Apply Palette"** to use the selection
3. Use **"Shuffle Variations"** to see different options

### **Color Harmony**
1. Choose a base color with the color picker
2. Select harmony type (Complementary, Triadic, etc.)
3. Click **"Generate Harmony"** for theory-based palettes

### **AI Color Names**
1. Click **"Generate Creative Names"** 
2. View unique AI-generated names for each color
3. Click again to toggle names on/off

### **Accessibility Testing**
1. Click **"Check WCAG Compliance"** for detailed analysis
2. Use colorblind simulation dropdown to test visibility
3. Review contrast ratios and accessibility ratings

### **Save & Export**
1. Click **"Save Current Palette"** to store locally
2. Use **"Export as JSON"** to download palette data
3. **"Import Palette"** to load saved files
4. Manage saved palettes in the dedicated section

## 🔧 **Technical Details**

### **File Structure**
```
nexus-palette-generator/
├── index.html          # Main HTML structure
├── style.css           # Cyberpunk styling & animations
├── script.js           # Core functionality & features
└── README.md           # Documentation
```

### **Key Technologies**
- **Pure HTML5/CSS3/JavaScript** - No frameworks required
- **CSS Grid & Flexbox** - Responsive layout system
- **CSS Custom Properties** - Dynamic theming
- **Local Storage API** - Palette persistence
- **File API** - Import/export functionality
- **Clipboard API** - Copy to clipboard feature

### **Performance Features**
- **Optimized Animations** - Hardware-accelerated CSS transforms
- **Efficient DOM Manipulation** - Minimal reflows and repaints
- **Lazy Loading** - Features load only when needed
- **Memory Management** - Proper cleanup of event listeners

## 🎨 **Customization**

### **Color Scheme**
Modify CSS custom properties in `style.css`:
```css
:root {
    --neon-cyan: #00ffff;
    --neon-pink: #ff0080;
    --neon-purple: #8000ff;
    /* Add your custom colors */
}
```

### **Add New Moods**
Extend the `recommendedPalettes` object in `script.js`:
```javascript
const recommendedPalettes = {
    yourMood: [
        ['#color1', '#color2', '#color3', '#color4', '#color5'],
        // Add more variations
    ]
};
```

## 🤝 **Contributing**

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

## 📄 **License**

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

## 🙏 **Acknowledgments**

- **Orbitron & Rajdhani Fonts** - Google Fonts
- **Font Awesome** - Icon library
- **Color Theory** - Traditional color harmony principles
- **WCAG Guidelines** - Web accessibility standards

## 🔮 **Future Features**

- 🎨 **Color Palette API Integration**
- 🤖 **Machine Learning Color Suggestions**
- 🌐 **Social Sharing Features**
- 📱 **Mobile App Version**
- 🎵 **Music-Based Color Generation**
- 🖼️ **Image Color Extraction**

---

<div align="center">

**Made with 💜 and lots of ☕**

[⭐ Star this repo](https://github.com/yourusername/nexus-palette-generator) | [🐛 Report Bug](https://github.com/yourusername/nexus-palette-generator/issues) | [💡 Request Feature](https://github.com/yourusername/nexus-palette-generator/issues)

</div>
