/* Basic Reset */

@import url(https://fonts.googleapis.com/css?family=Poppins:100,100italic,200,200italic,300,300italic,regular,italic,500,500italic,600,600italic,700,700italic,800,800italic,900,900italic);
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins';
}

body {
    background: linear-gradient(135deg, #83a4d4, #c3cfe2);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center; 
    padding: 20px;
}

.container {
    background-color: #fff;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    padding: 20px;
    max-width: 800px;
    width: 100%;
}

h1 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
    position: relative;
    padding-bottom: 0.5rem;
}

h1::after {
    content: " ";
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 75%;
    transform: translateX(-50%);
    height: 3px;
    background-color: #667eea;
    border-radius: 2px;
}

#btn {
    display: block;
    margin: 0 auto 2rem;
    padding: 0.7rem 1.3rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: #fff;
    border: none;
    border-radius: 3rem;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
    transition: transform 0.3s ease;
}

#btn:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

#btn:active {
    transform: translateY(0);
    box-shadow: none;
}

.palette-container {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 2rem;
}

.color-box {
    background-color: #f5f5f5;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.color-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.color {
    height: 120px;
    cursor: pointer;
}

.color-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.7rem 1rem;
    background-color: #fff;
    border-top: 1px solid #eee;
}

.hex-value {
    font-size: 0.9rem;
    letter-spacing: 0.05rem;
    color: #333;
}

.copy-btn {
    color: #8296ef;
    cursor: pointer;
    transition: color 0.3s ease;
}

.copy-btn:hover {
    color: #3a0052;
}

@media(max-width: 768px) {
    .palette-container {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
}