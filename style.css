/* Cyberpunk Futuristic Design */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --neon-cyan: #00ffff;
    --neon-pink: #ff0080;
    --neon-purple: #8000ff;
    --neon-green: #00ff80;
    --neon-yellow: #ffff00;
    --dark-bg: #0a0a0a;
    --darker-bg: #050505;
    --grid-color: #1a1a2e;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
}

body {
    font-family: 'Rajdhani', sans-serif;
    background: var(--dark-bg);
    background-image:
        radial-gradient(circle at 20% 80%, var(--neon-cyan) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, var(--neon-pink) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, var(--neon-purple) 0%, transparent 50%),
        linear-gradient(45deg, var(--darker-bg) 0%, var(--dark-bg) 100%);
    background-size: 100% 100%, 100% 100%, 100% 100%, 100% 100%;
    background-attachment: fixed;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: var(--text-primary);
    overflow-x: hidden;
    position: relative;
}

/* Animated Grid Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(var(--grid-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--grid-color) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
    z-index: -1;
    opacity: 0.3;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* Floating Particles */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(2px 2px at 20px 30px, var(--neon-cyan), transparent),
                radial-gradient(2px 2px at 40px 70px, var(--neon-pink), transparent),
                radial-gradient(1px 1px at 90px 40px, var(--neon-green), transparent),
                radial-gradient(1px 1px at 130px 80px, var(--neon-yellow), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: particleFloat 15s ease-in-out infinite;
    z-index: -1;
    opacity: 0.4;
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.container {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 2px solid var(--glass-border);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow:
        0 0 50px rgba(0, 255, 255, 0.1),
        inset 0 0 50px rgba(255, 255, 255, 0.02);
    max-width: 1200px;
    width: 100%;
    animation: containerGlow 3s ease-in-out infinite alternate;
    position: relative;
}

@keyframes containerGlow {
    0% {
        box-shadow:
            0 0 50px rgba(0, 255, 255, 0.1),
            inset 0 0 50px rgba(255, 255, 255, 0.02);
    }
    100% {
        box-shadow:
            0 0 80px rgba(0, 255, 255, 0.2),
            inset 0 0 50px rgba(255, 255, 255, 0.05);
    }
}

/* Holographic Corner Effects */
.container::before,
.container::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 2px solid var(--neon-cyan);
    z-index: 1;
}

.container::before {
    top: -2px;
    left: -2px;
    border-right: none;
    border-bottom: none;
    animation: cornerPulse1 2s ease-in-out infinite;
}

.container::after {
    bottom: -2px;
    right: -2px;
    border-left: none;
    border-top: none;
    animation: cornerPulse2 2s ease-in-out infinite;
}

@keyframes cornerPulse1 {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.1); }
}

@keyframes cornerPulse2 {
    0%, 100% { opacity: 0.5; transform: scale(1.1); }
    50% { opacity: 1; transform: scale(1); }
}

h1 {
    font-family: 'Orbitron', monospace;
    text-align: center;
    margin-bottom: 3rem;
    font-size: 3rem;
    font-weight: 900;
    text-transform: uppercase;
    letter-spacing: 3px;
    position: relative;
    background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink), var(--neon-purple));
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: neonTextGlow 3s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

@keyframes neonTextGlow {
    0%, 100% {
        background-position: 0% 50%;
        filter: brightness(1);
    }
    50% {
        background-position: 100% 50%;
        filter: brightness(1.2);
    }
}

h1::before {
    content: '◢';
    position: absolute;
    left: -50px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: var(--neon-cyan);
    animation: leftGlyph 2s ease-in-out infinite;
}

h1::after {
    content: '◣';
    position: absolute;
    right: -50px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 2rem;
    color: var(--neon-pink);
    animation: rightGlyph 2s ease-in-out infinite;
}

@keyframes leftGlyph {
    0%, 100% { transform: translateY(-50%) translateX(0); opacity: 1; }
    50% { transform: translateY(-50%) translateX(-10px); opacity: 0.7; }
}

@keyframes rightGlyph {
    0%, 100% { transform: translateY(-50%) translateX(0); opacity: 0.7; }
    50% { transform: translateY(-50%) translateX(10px); opacity: 1; }
}

h3 {
    font-family: 'Orbitron', monospace;
    color: var(--neon-cyan);
    font-size: 1.3rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 1.5rem;
    position: relative;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

h3::before {
    content: '▶';
    margin-right: 10px;
    color: var(--neon-pink);
    animation: iconPulse 1.5s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Cyberpunk Buttons */
.controls-section {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.btn {
    font-family: 'Rajdhani', sans-serif;
    padding: 1rem 2rem;
    background: linear-gradient(45deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    color: var(--neon-cyan);
    border: 2px solid var(--neon-cyan);
    border-radius: 0;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    clip-path: polygon(10px 0%, 100% 0%, calc(100% - 10px) 100%, 0% 100%);
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, var(--neon-cyan), transparent);
    transition: left 0.5s ease;
    opacity: 0.1;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, var(--neon-cyan) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    opacity: 0;
}

.btn:hover {
    color: var(--dark-bg);
    background: var(--neon-cyan);
    box-shadow:
        0 0 30px var(--neon-cyan),
        inset 0 0 30px rgba(0, 255, 255, 0.2);
    transform: translateY(-3px);
}

.btn:hover::before {
    left: 100%;
}

.btn:hover::after {
    width: 100%;
    height: 100%;
    opacity: 0.1;
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-color: var(--text-secondary);
    cursor: not-allowed;
    box-shadow: none;
    text-shadow: none;
}

.btn:disabled:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
}

/* Cyberpunk Sections */
.section {
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-cyan), var(--neon-pink), var(--neon-purple));
    animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.mood-controls, .harmony-controls, .ai-controls, .accessibility-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
    margin-bottom: 1.5rem;
}

.mood-select {
    font-family: 'Rajdhani', sans-serif;
    padding: 0.8rem 1.2rem;
    border: 2px solid var(--neon-cyan);
    border-radius: 0;
    background: rgba(0, 0, 0, 0.5);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 250px;
    clip-path: polygon(5px 0%, 100% 0%, calc(100% - 5px) 100%, 0% 100%);
}

.mood-select:focus {
    outline: none;
    border-color: var(--neon-pink);
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.3);
    background: rgba(255, 0, 128, 0.1);
}

.mood-select option {
    background: var(--dark-bg);
    color: var(--text-primary);
    padding: 0.5rem;
}

.color-input-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.color-input-wrapper label {
    font-family: 'Rajdhani', sans-serif;
    color: var(--neon-green);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(0, 255, 128, 0.5);
}

.color-input {
    width: 60px;
    height: 60px;
    border: 3px solid var(--neon-green);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    background: transparent;
    position: relative;
}

.color-input:hover {
    border-color: var(--neon-yellow);
    box-shadow: 0 0 30px rgba(255, 255, 0, 0.3);
    transform: scale(1.1);
}

.colorblind-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.colorblind-wrapper label {
    font-family: 'Rajdhani', sans-serif;
    color: var(--neon-purple);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(128, 0, 255, 0.5);
}

/* Cyberpunk Palette Container */
.palette-container {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    margin-bottom: 3rem;
}

.color-box {
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: 0;
    overflow: hidden;
    transition: all 0.4s ease;
    position: relative;
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 15px, 100% 100%, 15px 100%, 0 calc(100% - 15px));
}

.color-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--neon-cyan), var(--neon-pink));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.color-box:hover {
    transform: translateY(-10px) rotateX(5deg);
    border-color: var(--neon-cyan);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 255, 255, 0.3);
}

.color-box:hover::before {
    opacity: 0.1;
}

.color {
    height: 160px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--glass-border);
}

.color::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.color:hover::after {
    opacity: 1;
}

.color-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
}

.hex-value {
    font-family: 'Orbitron', monospace;
    font-size: 1rem;
    font-weight: 700;
    letter-spacing: 2px;
    color: var(--text-primary);
    text-transform: uppercase;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.color-name {
    font-family: 'Rajdhani', sans-serif !important;
    color: var(--neon-pink) !important;
    font-style: normal !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    text-shadow: 0 0 10px rgba(255, 0, 128, 0.5) !important;
}

/* Cyberpunk Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.8rem;
}

.lock-btn, .copy-btn {
    padding: 0.6rem;
    border: 1px solid var(--glass-border);
    background: rgba(0, 0, 0, 0.3);
    cursor: pointer;
    border-radius: 0;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    color: var(--text-secondary);
    clip-path: polygon(3px 0%, 100% 0%, calc(100% - 3px) 100%, 0% 100%);
}

.lock-btn {
    color: var(--text-secondary);
}

.lock-btn.locked {
    color: var(--neon-yellow);
    background: rgba(255, 255, 0, 0.1);
    border-color: var(--neon-yellow);
    box-shadow: 0 0 15px rgba(255, 255, 0, 0.3);
    text-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
}

.lock-btn:hover {
    color: var(--neon-yellow);
    background: rgba(255, 255, 0, 0.1);
    border-color: var(--neon-yellow);
    transform: scale(1.1);
}

.copy-btn {
    color: var(--text-secondary);
}

.copy-btn:hover {
    color: var(--neon-green);
    background: rgba(0, 255, 128, 0.1);
    border-color: var(--neon-green);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 255, 128, 0.3);
}

.copy-btn.copied {
    color: var(--neon-green);
    background: rgba(0, 255, 128, 0.2);
    border-color: var(--neon-green);
    box-shadow: 0 0 20px rgba(0, 255, 128, 0.5);
    text-shadow: 0 0 10px rgba(0, 255, 128, 0.8);
}

/* Export/Import Cyberpunk Style */
.export-import {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    align-items: center;
}

.file-input {
    display: none;
}

.file-label {
    font-family: 'Rajdhani', sans-serif;
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.5);
    border: 2px dashed var(--neon-purple);
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 600;
    color: var(--neon-purple);
    text-transform: uppercase;
    letter-spacing: 1px;
    clip-path: polygon(8px 0%, 100% 0%, calc(100% - 8px) 100%, 0% 100%);
    text-shadow: 0 0 10px rgba(128, 0, 255, 0.5);
}

.file-label:hover {
    border-color: var(--neon-pink);
    background: rgba(255, 0, 128, 0.1);
    color: var(--neon-pink);
    box-shadow: 0 0 20px rgba(255, 0, 128, 0.3);
    transform: translateY(-2px);
}

/* Accessibility Modal */
.accessibility-modal {
    backdrop-filter: blur(10px);
}

.accessibility-modal > div {
    background: var(--glass-bg) !important;
    border: 2px solid var(--glass-border) !important;
    color: var(--text-primary) !important;
    backdrop-filter: blur(20px) !important;
}

.accessibility-modal h3 {
    color: var(--neon-cyan) !important;
    text-align: center;
    margin-bottom: 2rem;
}

.rating-excellent { color: var(--neon-green); font-weight: bold; }
.rating-good { color: var(--neon-yellow); font-weight: bold; }
.rating-fair { color: var(--neon-pink); font-weight: bold; }
.rating-poor { color: #ff4757; font-weight: bold; }

/* Saved Palettes */
.saved-palette {
    background: var(--glass-bg) !important;
    border: 1px solid var(--glass-border) !important;
    backdrop-filter: blur(15px) !important;
    color: var(--text-primary) !important;
}

.saved-palette strong {
    color: var(--neon-cyan);
    font-family: 'Orbitron', monospace;
}

.saved-palette small {
    color: var(--text-secondary);
    font-family: 'Rajdhani', sans-serif;
}

/* Cyberpunk Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1.5rem;
        margin: 10px;
    }

    h1 {
        font-size: 2.2rem;
        letter-spacing: 2px;
    }

    h1::before,
    h1::after {
        display: none;
    }

    .palette-container {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 1.5rem;
    }

    .controls-section {
        flex-direction: column;
        gap: 1rem;
    }

    .mood-controls,
    .harmony-controls,
    .ai-controls,
    .accessibility-controls,
    .export-import {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .mood-select {
        min-width: auto;
        width: 100%;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .color-input-wrapper,
    .colorblind-wrapper {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 1rem;
    }

    h1 {
        font-size: 1.8rem;
        letter-spacing: 1px;
        margin-bottom: 2rem;
    }

    .palette-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .color {
        height: 120px;
    }

    .section {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    h3 {
        font-size: 1.1rem;
    }

    .btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-bg);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(var(--neon-cyan), var(--neon-pink));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--neon-pink), var(--neon-purple));
}

/* Selection Styling */
::selection {
    background: var(--neon-cyan);
    color: var(--dark-bg);
}

::-moz-selection {
    background: var(--neon-cyan);
    color: var(--dark-bg);
}