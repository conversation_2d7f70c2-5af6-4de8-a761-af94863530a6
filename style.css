/* Basic Reset */

@import url(https://fonts.googleapis.com/css?family=Poppins:100,100italic,200,200italic,300,300italic,regular,italic,500,500italic,600,600italic,700,700italic,800,800italic,900,900italic);
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins';
}

body {
    background: linear-gradient(135deg, #83a4d4, #c3cfe2);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center; 
    padding: 20px;
}

.container {
    background-color: #fff;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    padding: 20px;
    max-width: 800px;
    width: 100%;
}

h1 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: #333;
    position: relative;
    padding-bottom: 0.5rem;
};

h1::after {
    content: " ";
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 50%;
    transform: translateX(-50%);
    height: 3px;
    background-color: #667eea;
    border-radius: 2px;
}

#g