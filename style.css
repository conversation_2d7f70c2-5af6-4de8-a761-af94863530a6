/* Basic Reset */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 2rem;
    padding: 2.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 1000px;
    width: 100%;
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2d3748;
    position: relative;
    padding-bottom: 1rem;
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h1::after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 100px;
    transform: translateX(-50%);
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Control Buttons */
.controls-section {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

.btn {
    padding: 0.8rem 1.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    box-shadow: none;
}

.btn:disabled:hover {
    transform: none;
}

/* Sections */
.section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.section h3 {
    margin-bottom: 1rem;
    color: #2d3748;
    font-size: 1.2rem;
    font-weight: 600;
}

.mood-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

.mood-select {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    min-width: 200px;
}

.mood-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.palette-container {
    display: grid;
    gap: 1.2rem;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    margin-bottom: 2rem;
}

.color-box {
    background: white;
    border-radius: 1.2rem;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.color-box:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
}

.color {
    height: 140px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.color:hover {
    filter: brightness(1.1);
}

.color-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
}

.hex-value {
    font-size: 0.95rem;
    font-weight: 500;
    letter-spacing: 0.5px;
    color: #2d3748;
    font-family: 'Courier New', monospace;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.lock-btn, .copy-btn {
    padding: 0.4rem;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.lock-btn {
    color: #a0aec0;
}

.lock-btn.locked {
    color: #f6ad55;
    background: rgba(246, 173, 85, 0.1);
}

.lock-btn:hover {
    color: #f6ad55;
    background: rgba(246, 173, 85, 0.1);
}

.copy-btn {
    color: #667eea;
}

.copy-btn:hover {
    color: #5a67d8;
    background: rgba(102, 126, 234, 0.1);
}

.copy-btn.copied {
    color: #48bb78;
    background: rgba(72, 187, 120, 0.1);
}

/* Harmony Generator */
.harmony-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
}

.color-input-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.color-input {
    width: 50px;
    height: 40px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.color-input:hover {
    border-color: #667eea;
}

/* Export/Import */
.export-import {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.file-input {
    display: none;
}

.file-label {
    padding: 0.5rem 1rem;
    background: #f7fafc;
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.file-label:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1.5rem;
        margin: 10px;
    }

    h1 {
        font-size: 2rem;
    }

    .palette-container {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
    }

    .controls-section {
        flex-direction: column;
    }

    .mood-controls, .harmony-controls, .export-import {
        flex-direction: column;
        align-items: stretch;
    }

    .mood-select {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .palette-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .color {
        height: 100px;
    }
}